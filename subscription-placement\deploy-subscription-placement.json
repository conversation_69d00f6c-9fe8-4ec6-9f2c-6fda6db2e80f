{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Subscription Placement Wrapper", "version": "1.0.0"}, "description": "Wrapper template for deploying subscription placement module"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."}}, "managementSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated management subscription."}}, "connectivitySubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated connectivity subscription."}}, "identitySubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated identity subscription."}}, "singlePlatformSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the single platform subscription (lite deployment)."}}, "ldzPrdLegacySubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for production legacy applications."}}, "ldzPrdMicrosvcSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for production microservices applications."}}, "ldzNonPrdUatSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for UAT environment."}}, "ldzNonPrdDevSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for development environment."}}, "sandboxSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for sandbox environment."}}}, "variables": {"enableLite": "[not(empty(parameters('singlePlatformSubscriptionId')))]", "managementGroups": {"root": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "Landing Zone", "ldz-prd": "ldz-prd", "ldz-prd-legacy": "ldz-prd-legacy", "ldz-prd-microsvc": "ldz-prd-microsvc", "ldz-non-prd": "ldz-non-prd", "ldz-non-prd-uat": "ldz-non-prd-uat", "ldz-non-prd-dev": "ldz-non-prd-dev", "sandbox": "Sandbox", "decommissioned": "Decommissioned"}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2020-10-01", "name": "subscription-placement-deployment", "location": "[deployment().location]", "properties": {"mode": "Incremental", "templateLink": {"uri": "[uri(deployment().properties.templateLink.uri, 'subscription-placement.json')]", "contentVersion": "*******"}, "parameters": {"managementGroups": {"value": "[variables('managementGroups')]"}, "managementSubscriptionId": {"value": "[parameters('managementSubscriptionId')]"}, "connectivitySubscriptionId": {"value": "[parameters('connectivitySubscriptionId')]"}, "identitySubscriptionId": {"value": "[parameters('identitySubscriptionId')]"}, "singlePlatformSubscriptionId": {"value": "[parameters('singlePlatformSubscriptionId')]"}, "ldzPrdLegacySubscriptionId": {"value": "[parameters('ldzPrdLegacySubscriptionId')]"}, "ldzPrdMicrosvcSubscriptionId": {"value": "[parameters('ldzPrdMicrosvcSubscriptionId')]"}, "ldzNonPrdUatSubscriptionId": {"value": "[parameters('ldzNonPrdUatSubscriptionId')]"}, "ldzNonPrdDevSubscriptionId": {"value": "[parameters('ldzNonPrdDevSubscriptionId')]"}, "sandboxSubscriptionId": {"value": "[parameters('sandboxSubscriptionId')]"}, "enableLite": {"value": "[variables('enableLite')]"}}}}], "outputs": {"deploymentResult": {"type": "object", "value": "[reference('subscription-placement-deployment')]"}, "placedSubscriptions": {"type": "object", "value": "[reference('subscription-placement-deployment').outputs.placedSubscriptions.value]"}, "summary": {"type": "object", "value": "[reference('subscription-placement-deployment').outputs.summary.value]"}}}