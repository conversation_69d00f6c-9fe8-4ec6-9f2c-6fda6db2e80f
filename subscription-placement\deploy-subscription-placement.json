{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Subscription Placement Wrapper", "version": "1.0.0"}, "description": "Wrapper template for deploying subscription placement module"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."}}, "managementSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated management subscription."}}, "connectivitySubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated connectivity subscription."}}, "identitySubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated identity subscription."}}, "singlePlatformSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the single platform subscription (lite deployment)."}}, "corpLzSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for existing, empty subscriptions you want to move in as your first corp landing zones."}}, "onlineLzSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for existing, empty subscriptions you want to move in as your first online landing zones."}}, "corpConnectedLzSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for existing, empty subscriptions you want to move in as your first corp landing zones and connect to virtual networking hub."}}}, "variables": {"enableLite": "[not(empty(parameters('singlePlatformSubscriptionId')))]", "managementGroups": {"root": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-platform')]", "management": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-management')]", "connectivity": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-connectivity')]", "identity": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-identity')]", "landingZones": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-landingzones')]", "corp": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-corp')]", "online": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-online')]", "decommissioned": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-decommissioned')]", "sandbox": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-sandbox')]"}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2020-10-01", "name": "subscription-placement-deployment", "location": "[deployment().location]", "properties": {"mode": "Incremental", "templateLink": {"uri": "[uri(deployment().properties.templateLink.uri, 'subscription-placement.json')]", "contentVersion": "*******"}, "parameters": {"managementGroups": {"value": "[variables('managementGroups')]"}, "managementSubscriptionId": {"value": "[parameters('managementSubscriptionId')]"}, "connectivitySubscriptionId": {"value": "[parameters('connectivitySubscriptionId')]"}, "identitySubscriptionId": {"value": "[parameters('identitySubscriptionId')]"}, "singlePlatformSubscriptionId": {"value": "[parameters('singlePlatformSubscriptionId')]"}, "corpLzSubscriptionId": {"value": "[parameters('corpLzSubscriptionId')]"}, "onlineLzSubscriptionId": {"value": "[parameters('onlineLzSubscriptionId')]"}, "corpConnectedLzSubscriptionId": {"value": "[parameters('corpConnectedLzSubscriptionId')]"}, "enableLite": {"value": "[variables('enableLite')]"}}}}], "outputs": {"deploymentResult": {"type": "object", "value": "[reference('subscription-placement-deployment')]"}, "placedSubscriptions": {"type": "object", "value": "[reference('subscription-placement-deployment').outputs.placedSubscriptions.value]"}, "summary": {"type": "object", "value": "[reference('subscription-placement-deployment').outputs.summary.value]"}}}