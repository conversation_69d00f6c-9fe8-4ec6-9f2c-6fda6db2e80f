{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "metadata": {"description": "Provide the company prefix for the management group structure."}}}, "variables": {}, "resources": [{"type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[parameters('enterpriseScaleCompanyPrefix')]", "properties": {"displayName": "[parameters('enterpriseScaleCompanyPrefix')]", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', managementGroup().name)]"}}}}], "outputs": {"rootManagementGroup": {"type": "string", "value": "[parameters('enterpriseScaleCompanyPrefix')]"}}}