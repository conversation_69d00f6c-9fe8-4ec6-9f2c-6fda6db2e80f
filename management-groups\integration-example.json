{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "metadata": {
        "description": "V<PERSON> dụ về cách tích hợp Management Groups module vào main template"
    },
    "parameters": {
        "enterpriseScaleCompanyPrefix": {
            "type": "string",
            "metadata": {
                "description": "Company prefix for the management group structure"
            }
        },
        "singlePlatformSubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Single platform subscription ID for lite deployment"
            }
        },
        "managementSubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Management subscription ID"
            }
        },
        "connectivitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Connectivity subscription ID"
            }
        },
        "identitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Identity subscription ID"
            }
        }
    },
    "variables": {
        "deploymentSuffix": "[concat('-', deployment().location, '-', guid(parameters('enterpriseScaleCompanyPrefix')))]",
        "deploymentNames": {
            "mgmtGroupDeploymentName": "[take(concat('alz-Mgs', variables('deploymentSuffix')), 64)]",
            "subscriptionPlacementDeploymentName": "[take(concat('alz-SubPlacement', variables('deploymentSuffix')), 64)]"
        }
    },
    "resources": [
        {
            // Deploy Management Groups using the module
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2021-04-01",
            "name": "[variables('deploymentNames').mgmtGroupDeploymentName]",
            "location": "[deployment().location]",
            "properties": {
                "mode": "Incremental",
                "templateLink": {
                    "contentVersion": "*******",
                    "uri": "[uri(deployment().properties.templateLink.uri, 'management-groups/management-groups.json')]"
                },
                "parameters": {
                    "enterpriseScaleCompanyPrefix": {
                        "value": "[parameters('enterpriseScaleCompanyPrefix')]"
                    },
                    "enableLite": {
                        "value": "[not(empty(parameters('singlePlatformSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Example: Place management subscription into management group
            "condition": "[not(empty(parameters('managementSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').subscriptionPlacementDeploymentName, '-mgmt')]",
            "location": "[deployment().location]",
            "scope": "[reference(variables('deploymentNames').mgmtGroupDeploymentName).outputs.scopes.value.managementManagementGroup]",
            "dependsOn": [
                "[resourceId('Microsoft.Resources/deployments', variables('deploymentNames').mgmtGroupDeploymentName)]"
            ],
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[reference(variables('deploymentNames').mgmtGroupDeploymentName).outputs.managementGroups.value.management]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('managementSubscriptionId')]"
                    }
                }
            }
        }
    ],
    "outputs": {
        "managementGroups": {
            "type": "object",
            "value": "[reference(variables('deploymentNames').mgmtGroupDeploymentName).outputs.managementGroups.value]"
        },
        "scopes": {
            "type": "object",
            "value": "[reference(variables('deploymentNames').mgmtGroupDeploymentName).outputs.scopes.value]"
        },
        "mgmtGroupsArray": {
            "type": "array",
            "value": "[reference(variables('deploymentNames').mgmtGroupDeploymentName).outputs.mgmtGroupsArray.value]"
        }
    }
}
