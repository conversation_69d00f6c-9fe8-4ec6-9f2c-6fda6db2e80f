# Deploy Azure Landing Zone Management Groups
# Script để deploy management groups structure

param(
    [Parameter(Mandatory = $true)]
    [string]$EnterpriseScaleCompanyPrefix,
    
    [Parameter(Mandatory = $false)]
    [string]$SinglePlatformSubscriptionId = "",
    
    [Parameter(Mandatory = $false)]
    [string]$Location = "East US",
    
    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupId = "",
    
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Import required modules
Import-Module Az.Accounts -Force
Import-Module Az.Resources -Force

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Bạn chưa đăng nhập Azure. <PERSON>ang thực hiện đăng nhập..." -ForegroundColor Yellow
    Connect-AzAccount
}

# Set management group scope
if ([string]::IsNullOrEmpty($ManagementGroupId)) {
    $ManagementGroupId = (Get-AzContext).Tenant.Id
    Write-Host "Sử dụng Tenant Root Group: $ManagementGroupId" -ForegroundColor Green
}

# Prepare parameters
$templateFile = Join-Path $PSScriptRoot "management-groups.json"
$parametersFile = Join-Path $PSScriptRoot "management-groups.parameters.json"

# Check if files exist
if (-not (Test-Path $templateFile)) {
    Write-Error "Không tìm thấy template file: $templateFile"
    exit 1
}

if (-not (Test-Path $parametersFile)) {
    Write-Error "Không tìm thấy parameters file: $parametersFile"
    exit 1
}

# Prepare deployment parameters
$deploymentParams = @{
    ManagementGroupId = $ManagementGroupId
    Location = $Location
    TemplateFile = $templateFile
    TemplateParameterFile = $parametersFile
    enterpriseScaleCompanyPrefix = $EnterpriseScaleCompanyPrefix
}

# Add single platform subscription if provided
if (-not [string]::IsNullOrEmpty($SinglePlatformSubscriptionId)) {
    $deploymentParams.Add("enableLite", $true)
    Write-Host "Deploying in Lite mode với single platform subscription: $SinglePlatformSubscriptionId" -ForegroundColor Yellow
} else {
    $deploymentParams.Add("enableLite", $false)
    Write-Host "Deploying in Full mode" -ForegroundColor Green
}

# Generate deployment name
$deploymentName = "alz-mgmt-groups-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$deploymentParams.Add("Name", $deploymentName)

Write-Host "=== Azure Landing Zone Management Groups Deployment ===" -ForegroundColor Cyan
Write-Host "Company Prefix: $EnterpriseScaleCompanyPrefix" -ForegroundColor White
Write-Host "Management Group Scope: $ManagementGroupId" -ForegroundColor White
Write-Host "Location: $Location" -ForegroundColor White
Write-Host "Deployment Name: $deploymentName" -ForegroundColor White

if ($WhatIf) {
    Write-Host "Chạy What-If deployment..." -ForegroundColor Yellow
    $result = New-AzManagementGroupDeployment @deploymentParams -WhatIf
} else {
    Write-Host "Bắt đầu deployment..." -ForegroundColor Green
    try {
        $result = New-AzManagementGroupDeployment @deploymentParams -Verbose
        
        if ($result.ProvisioningState -eq "Succeeded") {
            Write-Host "✅ Deployment thành công!" -ForegroundColor Green
            Write-Host "Management Groups đã được tạo:" -ForegroundColor Cyan
            
            $mgmtGroups = $result.Outputs.managementGroups.Value
            foreach ($mg in $mgmtGroups.PSObject.Properties) {
                Write-Host "  - $($mg.Name): $($mg.Value)" -ForegroundColor White
            }
        } else {
            Write-Host "❌ Deployment thất bại với trạng thái: $($result.ProvisioningState)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Lỗi trong quá trình deployment:" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        exit 1
    }
}

Write-Host "=== Hoàn thành ===" -ForegroundColor Cyan
