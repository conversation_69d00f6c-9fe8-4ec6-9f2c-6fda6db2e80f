{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "metadata": {
        "_generator": {
            "name": "ALZ Subscription Placement Module",
            "version": "1.0.0"
        },
        "description": "This template places subscriptions into appropriate management groups"
    },
    "parameters": {
        "managementGroups": {
            "type": "object",
            "metadata": {
                "description": "Object containing management group names"
            }
        },
        "managementSubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Management subscription ID"
            }
        },
        "connectivitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Connectivity subscription ID"
            }
        },
        "identitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Identity subscription ID"
            }
        },
        "singlePlatformSubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Single platform subscription ID for lite deployment"
            }
        },
        "corpLzSubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of corp landing zone subscription IDs"
            }
        },
        "onlineLzSubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of online landing zone subscription IDs"
            }
        },
        "corpConnectedLzSubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of corp connected landing zone subscription objects"
            }
        },
        "enableLite": {
            "type": "bool",
            "defaultValue": false,
            "metadata": {
                "description": "Enable lite deployment mode"
            }
        }
    },
    "variables": {
        "deploymentSuffix": "[concat('-', deployment().location, '-', guid(deployment().name))]",
        "scopes": {
            "managementManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').management))]",
            "connectivityManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').connectivity))]",
            "identityManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').identity))]",
            "platformManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform)]",
            "corpManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').corp)]",
            "onlineManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').online)]"
        },
        "deploymentNames": {
            "mgmtSubscriptionPlacement": "[take(concat('alz-MgmtSub', variables('deploymentSuffix')), 64)]",
            "connectivitySubscriptionPlacement": "[take(concat('alz-ConnectivitySub', variables('deploymentSuffix')), 64)]",
            "identitySubscriptionPlacement": "[take(concat('alz-IdentitySub', variables('deploymentSuffix')), 64)]",
            "platformLiteSubscriptionPlacement": "[take(concat('alz-PlatformSubLite', variables('deploymentSuffix')), 64)]",
            "onlineLzSubs": "[take(concat('alz-OnlineLzs', variables('deploymentSuffix')), 60)]",
            "corpLzSubs": "[take(concat('alz-CorpLzs', variables('deploymentSuffix')), 60)]",
            "corpConnectedMoveLzSubs": "[take(concat('alz-CorpConnLzs', variables('deploymentSuffix')), 50)]"
        }
    },
    "resources": [
        {
            // Placing management subscription into dedicated management group
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))), not(parameters('enableLite')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').mgmtSubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').managementManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').management]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('managementSubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing connectivity subscription into dedicated management group
            "condition": "[and(not(empty(parameters('connectivitySubscriptionId'))), not(parameters('enableLite')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').connectivitySubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').connectivityManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').connectivity]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('connectivitySubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing identity subscription into dedicated management group
            "condition": "[and(not(empty(parameters('identitySubscriptionId'))), not(parameters('enableLite')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').identitySubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').identityManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').identity]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('identitySubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing Platform subscription into platform management group (Lite mode)
            "condition": "[not(empty(parameters('singlePlatformSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').platformLiteSubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').platformManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').platform]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('singlePlatformSubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing subscription(s) into online landing zone management group
            "condition": "[not(empty(parameters('onlineLzSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').onlineLzSubs, copyIndex())]",
            "scope": "[variables('scopes').onlineManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "onlineLzs",
                "count": "[length(parameters('onlineLzSubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').online]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('onlineLzSubscriptionId')[copyIndex()]]"
                    }
                }
            }
        },
        {
            // Placing subscriptions into corp landing zone management group
            "condition": "[not(empty(parameters('corpLzSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').corpLzSubs, copyIndex())]",
            "scope": "[variables('scopes').corpManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "corpLzs",
                "count": "[length(parameters('corpLzSubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').corp]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('corpLzSubscriptionId')[copyIndex()]]"
                    }
                }
            }
        },
        {
            // Placing subscriptions into corp landing zone management group (connected)
            "condition": "[not(empty(parameters('corpConnectedLzSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').corpConnectedMoveLzSubs, copyIndex())]",
            "scope": "[variables('scopes').corpManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "corpConnectedMoveLzs",
                "count": "[length(parameters('corpConnectedLzSubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').corp]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('corpConnectedLzSubscriptionId')[copyIndex()].subs]"
                    }
                }
            }
        }
    ],
    "outputs": {
        "deploymentNames": {
            "type": "object",
            "value": "[variables('deploymentNames')]"
        },
        "placedSubscriptions": {
            "type": "object",
            "value": {
                "management": "[if(and(not(empty(parameters('managementSubscriptionId'))), not(parameters('enableLite'))), parameters('managementSubscriptionId'), '')]",
                "connectivity": "[if(and(not(empty(parameters('connectivitySubscriptionId'))), not(parameters('enableLite'))), parameters('connectivitySubscriptionId'), '')]",
                "identity": "[if(and(not(empty(parameters('identitySubscriptionId'))), not(parameters('enableLite'))), parameters('identitySubscriptionId'), '')]",
                "platform": "[if(not(empty(parameters('singlePlatformSubscriptionId'))), parameters('singlePlatformSubscriptionId'), '')]",
                "onlineLandingZones": "[parameters('onlineLzSubscriptionId')]",
                "corpLandingZones": "[parameters('corpLzSubscriptionId')]",
                "corpConnectedLandingZones": "[parameters('corpConnectedLzSubscriptionId')]"
            }
        },
        "summary": {
            "type": "object",
            "value": {
                "totalSubscriptionsPlaced": "[add(add(add(add(add(add(if(and(not(empty(parameters('managementSubscriptionId'))), not(parameters('enableLite'))), 1, 0), if(and(not(empty(parameters('connectivitySubscriptionId'))), not(parameters('enableLite'))), 1, 0)), if(and(not(empty(parameters('identitySubscriptionId'))), not(parameters('enableLite'))), 1, 0)), if(not(empty(parameters('singlePlatformSubscriptionId'))), 1, 0)), length(parameters('onlineLzSubscriptionId'))), length(parameters('corpLzSubscriptionId'))), length(parameters('corpConnectedLzSubscriptionId')))]",
                "deploymentMode": "[if(parameters('enableLite'), 'Lite', 'Full')]",
                "platformSubscriptionsCount": "[if(parameters('enableLite'), if(not(empty(parameters('singlePlatformSubscriptionId'))), 1, 0), add(add(if(not(empty(parameters('managementSubscriptionId'))), 1, 0), if(not(empty(parameters('connectivitySubscriptionId'))), 1, 0)), if(not(empty(parameters('identitySubscriptionId'))), 1, 0)))]",
                "landingZoneSubscriptionsCount": "[add(add(length(parameters('onlineLzSubscriptionId')), length(parameters('corpLzSubscriptionId'))), length(parameters('corpConnectedLzSubscriptionId')))]"
            }
        }
    }
}
