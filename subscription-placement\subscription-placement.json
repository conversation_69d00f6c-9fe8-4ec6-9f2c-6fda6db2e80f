{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "metadata": {
        "_generator": {
            "name": "ALZ Subscription Placement Module",
            "version": "1.0.0"
        },
        "description": "This template places subscriptions into appropriate management groups"
    },
    "parameters": {
        "managementGroups": {
            "type": "object",
            "metadata": {
                "description": "Object containing management group names"
            }
        },
        "managementSubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Management subscription ID"
            }
        },
        "connectivitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Connectivity subscription ID"
            }
        },
        "identitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Identity subscription ID"
            }
        },
        "singlePlatformSubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Single platform subscription ID for lite deployment"
            }
        },
        "ldzPrdLegacySubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of production legacy application subscription IDs"
            }
        },
        "ldzPrdMicrosvcSubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of production microservices application subscription IDs"
            }
        },
        "ldzNonPrdUatSubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of UAT environment subscription IDs"
            }
        },
        "ldzNonPrdDevSubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of development environment subscription IDs"
            }
        },
        "sandboxSubscriptionId": {
            "type": "array",
            "defaultValue": [],
            "metadata": {
                "description": "Array of sandbox environment subscription IDs"
            }
        },
        "enableLite": {
            "type": "bool",
            "defaultValue": false,
            "metadata": {
                "description": "Enable lite deployment mode"
            }
        }
    },
    "variables": {
        "deploymentSuffix": "[concat('-', deployment().location, '-', guid(deployment().name))]",
        "scopes": {
            "managementManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').management))]",
            "connectivityManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').connectivity))]",
            "platformManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform)]",
            "ldzPrdLegacyManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups')['ldz-prd-legacy'])]",
            "ldzPrdMicrosvcManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups')['ldz-prd-microsvc'])]",
            "ldzNonPrdUatManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups')['ldz-non-prd-uat'])]",
            "ldzNonPrdDevManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups')['ldz-non-prd-dev'])]",
            "sandboxManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').sandbox)]"
        },
        "deploymentNames": {
            "mgmtSubscriptionPlacement": "[take(concat('alz-MgmtSub', variables('deploymentSuffix')), 64)]",
            "connectivitySubscriptionPlacement": "[take(concat('alz-ConnectivitySub', variables('deploymentSuffix')), 64)]",
            "platformLiteSubscriptionPlacement": "[take(concat('alz-PlatformSubLite', variables('deploymentSuffix')), 64)]",
            "ldzPrdLegacySubs": "[take(concat('alz-PrdLegacyLzs', variables('deploymentSuffix')), 60)]",
            "ldzPrdMicrosvcSubs": "[take(concat('alz-PrdMicrosvcLzs', variables('deploymentSuffix')), 60)]",
            "ldzNonPrdUatSubs": "[take(concat('alz-UatLzs', variables('deploymentSuffix')), 60)]",
            "ldzNonPrdDevSubs": "[take(concat('alz-DevLzs', variables('deploymentSuffix')), 60)]",
            "sandboxSubs": "[take(concat('alz-SandboxLzs', variables('deploymentSuffix')), 60)]"
        }
    },
    "resources": [
        {
            // Placing management subscription into dedicated management group
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))), not(parameters('enableLite')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').mgmtSubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').managementManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').management]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('managementSubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing connectivity subscription into dedicated management group
            "condition": "[and(not(empty(parameters('connectivitySubscriptionId'))), not(parameters('enableLite')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').connectivitySubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').connectivityManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').connectivity]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('connectivitySubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing identity subscription into dedicated management group
            "condition": "[and(not(empty(parameters('identitySubscriptionId'))), not(parameters('enableLite')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').identitySubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').identityManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').identity]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('identitySubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing Platform subscription into platform management group (Lite mode)
            "condition": "[not(empty(parameters('singlePlatformSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[variables('deploymentNames').platformLiteSubscriptionPlacement]",
            "location": "[deployment().location]",
            "scope": "[variables('scopes').platformManagementGroup]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').platform]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('singlePlatformSubscriptionId')]"
                    }
                }
            }
        },
        {
            // Placing subscription(s) into production legacy landing zone management group
            "condition": "[not(empty(parameters('ldzPrdLegacySubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').ldzPrdLegacySubs, copyIndex())]",
            "scope": "[variables('scopes').ldzPrdLegacyManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "ldzPrdLegacySubs",
                "count": "[length(parameters('ldzPrdLegacySubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups')['ldz-prd-legacy']]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('ldzPrdLegacySubscriptionId')[copyIndex()]]"
                    }
                }
            }
        },
        {
            // Placing subscriptions into production microservices landing zone management group
            "condition": "[not(empty(parameters('ldzPrdMicrosvcSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').ldzPrdMicrosvcSubs, copyIndex())]",
            "scope": "[variables('scopes').ldzPrdMicrosvcManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "ldzPrdMicrosvcSubs",
                "count": "[length(parameters('ldzPrdMicrosvcSubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups')['ldz-prd-microsvc']]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('ldzPrdMicrosvcSubscriptionId')[copyIndex()]]"
                    }
                }
            }
        },
        {
            // Placing subscriptions into UAT environment management group
            "condition": "[not(empty(parameters('ldzNonPrdUatSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').ldzNonPrdUatSubs, copyIndex())]",
            "scope": "[variables('scopes').ldzNonPrdUatManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "ldzNonPrdUatSubs",
                "count": "[length(parameters('ldzNonPrdUatSubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups')['ldz-non-prd-uat']]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('ldzNonPrdUatSubscriptionId')[copyIndex()]]"
                    }
                }
            }
        },
        {
            // Placing subscriptions into Development environment management group
            "condition": "[not(empty(parameters('ldzNonPrdDevSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').ldzNonPrdDevSubs, copyIndex())]",
            "scope": "[variables('scopes').ldzNonPrdDevManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "ldzNonPrdDevSubs",
                "count": "[length(parameters('ldzNonPrdDevSubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups')['ldz-non-prd-dev']]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('ldzNonPrdDevSubscriptionId')[copyIndex()]]"
                    }
                }
            }
        },
        {
            // Placing subscriptions into Sandbox environment management group
            "condition": "[not(empty(parameters('sandboxSubscriptionId')))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2020-10-01",
            "name": "[concat(variables('deploymentNames').sandboxSubs, copyIndex())]",
            "scope": "[variables('scopes').sandboxManagementGroup]",
            "location": "[deployment().location]",
            "copy": {
                "name": "sandboxSubs",
                "count": "[length(parameters('sandboxSubscriptionId'))]"
            },
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        "targetManagementGroupId": {
                            "type": "string"
                        },
                        "subscriptionId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-05-01",
                            "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]",
                            "properties": {}
                        }
                    ]
                },
                "parameters": {
                    "targetManagementGroupId": {
                        "value": "[parameters('managementGroups').sandbox]"
                    },
                    "subscriptionId": {
                        "value": "[parameters('sandboxSubscriptionId')[copyIndex()]]"
                    }
                }
            }
        }
    ],
    "outputs": {
        "deploymentNames": {
            "type": "object",
            "value": "[variables('deploymentNames')]"
        },
        "placedSubscriptions": {
            "type": "object",
            "value": {
                "management": "[if(and(not(empty(parameters('managementSubscriptionId'))), not(parameters('enableLite'))), parameters('managementSubscriptionId'), '')]",
                "connectivity": "[if(and(not(empty(parameters('connectivitySubscriptionId'))), not(parameters('enableLite'))), parameters('connectivitySubscriptionId'), '')]",
                "platform": "[if(not(empty(parameters('singlePlatformSubscriptionId'))), parameters('singlePlatformSubscriptionId'), '')]",
                "ldzPrdLegacy": "[parameters('ldzPrdLegacySubscriptionId')]",
                "ldzPrdMicrosvc": "[parameters('ldzPrdMicrosvcSubscriptionId')]",
                "ldzNonPrdUat": "[parameters('ldzNonPrdUatSubscriptionId')]",
                "ldzNonPrdDev": "[parameters('ldzNonPrdDevSubscriptionId')]",
                "sandbox": "[parameters('sandboxSubscriptionId')]"
            }
        },
        "summary": {
            "type": "object",
            "value": {
                "totalSubscriptionsPlaced": "[add(add(add(add(add(add(add(if(and(not(empty(parameters('managementSubscriptionId'))), not(parameters('enableLite'))), 1, 0), if(and(not(empty(parameters('connectivitySubscriptionId'))), not(parameters('enableLite'))), 1, 0)), if(not(empty(parameters('singlePlatformSubscriptionId'))), 1, 0)), length(parameters('ldzPrdLegacySubscriptionId'))), length(parameters('ldzPrdMicrosvcSubscriptionId'))), length(parameters('ldzNonPrdUatSubscriptionId'))), length(parameters('ldzNonPrdDevSubscriptionId'))), length(parameters('sandboxSubscriptionId')))]",
                "deploymentMode": "[if(parameters('enableLite'), 'Lite', 'Full')]",
                "platformSubscriptionsCount": "[if(parameters('enableLite'), if(not(empty(parameters('singlePlatformSubscriptionId'))), 1, 0), add(if(not(empty(parameters('managementSubscriptionId'))), 1, 0), if(not(empty(parameters('connectivitySubscriptionId'))), 1, 0)))]",
                "landingZoneSubscriptionsCount": "[add(add(add(add(length(parameters('ldzPrdLegacySubscriptionId')), length(parameters('ldzPrdMicrosvcSubscriptionId'))), length(parameters('ldzNonPrdUatSubscriptionId'))), length(parameters('ldzNonPrdDevSubscriptionId'))), length(parameters('sandboxSubscriptionId')))]",
                "productionSubscriptionsCount": "[add(length(parameters('ldzPrdLegacySubscriptionId')), length(parameters('ldzPrdMicrosvcSubscriptionId')))]",
                "nonProductionSubscriptionsCount": "[add(length(parameters('ldzNonPrdUatSubscriptionId')), length(parameters('ldzNonPrdDevSubscriptionId')))]",
                "sandboxSubscriptionsCount": "[length(parameters('sandboxSubscriptionId'))]"
            }
        }
    }
}
