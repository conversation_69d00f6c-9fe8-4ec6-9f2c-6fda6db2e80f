{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Management Groups Deployment Wrapper", "version": "1.0.0"}, "description": "Wrapper template để deploy management groups từ main template"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "metadata": {"description": "Company prefix for the management group structure"}}, "singlePlatformSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Single platform subscription ID for lite deployment"}}, "templateUri": {"type": "string", "defaultValue": "[uri(deployment().properties.templateLink.uri, 'management-groups.json')]", "metadata": {"description": "URI to the management groups template"}}}, "variables": {"enableLite": "[not(empty(parameters('singlePlatformSubscriptionId')))]", "deploymentSuffix": "[concat('-', deployment().location, '-', guid(parameters('enterpriseScaleCompanyPrefix')))]", "deploymentName": "[take(concat('alz-MgmtGroups', variables('deploymentSuffix')), 64)]"}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[variables('deploymentName')]", "location": "[deployment().location]", "properties": {"mode": "Incremental", "templateLink": {"contentVersion": "*******", "uri": "[parameters('templateUri')]"}, "parameters": {"enterpriseScaleCompanyPrefix": {"value": "[parameters('enterpriseScaleCompanyPrefix')]"}, "enableLite": {"value": "[variables('enableLite')]"}}}}], "outputs": {"managementGroups": {"type": "object", "value": "[reference(variables('deploymentName')).outputs.managementGroups.value]"}, "scopes": {"type": "object", "value": "[reference(variables('deploymentName')).outputs.scopes.value]"}, "mgmtGroupsArray": {"type": "array", "value": "[reference(variables('deploymentName')).outputs.mgmtGroupsArray.value]"}, "enableLite": {"type": "bool", "value": "[variables('enableLite')]"}}}