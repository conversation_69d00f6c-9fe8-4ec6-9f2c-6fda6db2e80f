# Azure Landing Zone Management Groups Module

Đây là module tạo cấu trúc management group cho Azure Landing Zone.

## Mô tả

Module này tạo ra cấu trúc management group theo best practices của Azure Landing Zone, bao gồm:

### Full Deployment (enableLite = false)
- **Root Management Group**: `{prefix}`
  - **Platform Management Group**: `{prefix}-platform`
    - **Management**: `{prefix}-management`
    - **Connectivity**: `{prefix}-connectivity`
    - **Identity**: `{prefix}-identity`
  - **Landing Zones**: `{prefix}-landingzones`
    - **Corp**: `{prefix}-corp`
    - **Online**: `{prefix}-online`
  - **Decommissioned**: `{prefix}-decommissioned`
  - **Sandboxes**: `{prefix}-sandboxes`

### Lite Deployment (enableLite = true)
- **Root Management Group**: `{prefix}`
  - **Platform Management Group**: `{prefix}-platform` (consolidated)
  - **Landing Zones**: `{prefix}-landingzones`
    - **Corp**: `{prefix}-corp`
    - **Online**: `{prefix}-online`
  - **Decommissioned**: `{prefix}-decommissioned`
  - **Sandboxes**: `{prefix}-sandboxes`

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enterpriseScaleCompanyPrefix` | string | - | Company prefix cho management group structure |
| `enableLite` | bool | false | Enable Enterprise Scale Lite deployment (single platform subscription) |

## Outputs

| Output | Type | Description |
|--------|------|-------------|
| `managementGroups` | object | Object chứa tên của tất cả management groups |
| `scopes` | object | Object chứa resource IDs của tất cả management groups |
| `mgmtGroupsArray` | array | Array chứa tên của tất cả management groups |

## Cách sử dụng

### Deploy trực tiếp
```bash
az deployment mg create \
  --management-group-id <tenant-root-group-id> \
  --location <location> \
  --template-file management-groups.json \
  --parameters @management-groups.parameters.json
```

### Sử dụng như module trong template khác
```json
{
  "type": "Microsoft.Resources/deployments",
  "apiVersion": "2021-04-01",
  "name": "managementGroups",
  "location": "[deployment().location]",
  "properties": {
    "mode": "Incremental",
    "templateLink": {
      "uri": "[uri(deployment().properties.templateLink.uri, 'management-groups/management-groups.json')]"
    },
    "parameters": {
      "enterpriseScaleCompanyPrefix": {
        "value": "[parameters('enterpriseScaleCompanyPrefix')]"
      },
      "enableLite": {
        "value": "[not(empty(parameters('singlePlatformSubscriptionId')))]"
      }
    }
  }
}
```

## Lưu ý

- Module này cần được deploy ở scope management group level
- Cần có quyền Management Group Contributor hoặc Owner
- Trong lite mode, management, connectivity và identity sẽ được consolidate vào platform management group
