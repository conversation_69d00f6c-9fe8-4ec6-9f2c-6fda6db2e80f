# Azure Landing Zone - Subscription Placement Module

## 📋 Overview

This module handles the placement of Azure subscriptions into appropriate management groups within the Azure Landing Zone (ALZ) hierarchy. It supports both **Full** and **Lite** deployment modes and can place various types of subscriptions including platform subscriptions and landing zone subscriptions.

## 🏗️ Architecture

The module places subscriptions into the following management group structure:

```
Root Management Group (e.g., "contoso")
├── Platform
│   ├── Management (Full mode only)
│   ├── Connectivity (Full mode only)
│   ├── Identity (Full mode only)
│   └── [Single Platform Subscription] (Lite mode only)
└── Landing Zones
    ├── Corp
    │   ├── [Corp Landing Zone Subscriptions]
    │   └── [Corp Connected Landing Zone Subscriptions]
    └── Online
        └── [Online Landing Zone Subscriptions]
```

## 📁 Module Structure

```
subscription-placement/
├── subscription-placement.json              # Core template
├── subscription-placement.parameters.json   # Parameters file
├── deploy-subscription-placement.json       # Wrapper template
├── Deploy-SubscriptionPlacement.ps1        # PowerShell deployment script
├── integration-example.json                # Integration example
└── README.md                               # This documentation
```

## 🚀 Deployment Modes

### Full Deployment Mode
- **Management Subscription** → `{prefix}-management` MG
- **Connectivity Subscription** → `{prefix}-connectivity` MG  
- **Identity Subscription** → `{prefix}-identity` MG
- **Landing Zone Subscriptions** → Respective landing zone MGs

### Lite Deployment Mode
- **Single Platform Subscription** → `{prefix}-platform` MG
- **Landing Zone Subscriptions** → Respective landing zone MGs

## 📝 Parameters

### Core Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `managementGroups` | object | Yes | Management group names object |
| `enableLite` | bool | No | Enable lite deployment mode (default: false) |

### Platform Subscription Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `managementSubscriptionId` | string | No | Management subscription ID (Full mode) |
| `connectivitySubscriptionId` | string | No | Connectivity subscription ID (Full mode) |
| `identitySubscriptionId` | string | No | Identity subscription ID (Full mode) |
| `singlePlatformSubscriptionId` | string | No | Single platform subscription ID (Lite mode) |

### Landing Zone Subscription Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `corpLzSubscriptionId` | array | No | Corp landing zone subscription IDs |
| `onlineLzSubscriptionId` | array | No | Online landing zone subscription IDs |
| `corpConnectedLzSubscriptionId` | array | No | Corp connected landing zone subscription objects |

## 🔧 Usage Examples

### Standalone Deployment

#### Full Mode Deployment
```powershell
.\Deploy-SubscriptionPlacement.ps1 `
    -EnterpriseScaleCompanyPrefix "contoso" `
    -Location "East US" `
    -ManagementSubscriptionId "12345678-1234-1234-1234-123456789012" `
    -ConnectivitySubscriptionId "12345678-1234-1234-1234-123456789013" `
    -IdentitySubscriptionId "12345678-1234-1234-1234-123456789014"
```

#### Lite Mode Deployment
```powershell
.\Deploy-SubscriptionPlacement.ps1 `
    -EnterpriseScaleCompanyPrefix "contoso" `
    -Location "East US" `
    -SinglePlatformSubscriptionId "12345678-1234-1234-1234-123456789012"
```

#### With Landing Zone Subscriptions
```powershell
.\Deploy-SubscriptionPlacement.ps1 `
    -EnterpriseScaleCompanyPrefix "contoso" `
    -Location "East US" `
    -SinglePlatformSubscriptionId "12345678-1234-1234-1234-123456789012" `
    -CorpLzSubscriptionId @("sub1", "sub2") `
    -OnlineLzSubscriptionId @("sub3", "sub4")
```

### Integration with Main Template

```json
{
    "type": "Microsoft.Resources/deployments",
    "apiVersion": "2020-10-01",
    "name": "subscription-placement",
    "location": "[deployment().location]",
    "dependsOn": [
        "[resourceId('Microsoft.Resources/deployments', 'management-groups')]"
    ],
    "properties": {
        "mode": "Incremental",
        "templateLink": {
            "uri": "[uri(deployment().properties.templateLink.uri, 'subscription-placement/deploy-subscription-placement.json')]",
            "contentVersion": "*******"
        },
        "parameters": {
            "enterpriseScaleCompanyPrefix": {
                "value": "[parameters('enterpriseScaleCompanyPrefix')]"
            },
            "managementSubscriptionId": {
                "value": "[parameters('managementSubscriptionId')]"
            },
            "connectivitySubscriptionId": {
                "value": "[parameters('connectivitySubscriptionId')]"
            },
            "identitySubscriptionId": {
                "value": "[parameters('identitySubscriptionId')]"
            },
            "singlePlatformSubscriptionId": {
                "value": "[parameters('singlePlatformSubscriptionId')]"
            },
            "corpLzSubscriptionId": {
                "value": "[parameters('corpLzSubscriptionId')]"
            },
            "onlineLzSubscriptionId": {
                "value": "[parameters('onlineLzSubscriptionId')]"
            },
            "corpConnectedLzSubscriptionId": {
                "value": "[parameters('corpConnectedLzSubscriptionId')]"
            }
        }
    }
}
```

## 📤 Outputs

### `deploymentNames`
Object containing all deployment names used by the module.

### `placedSubscriptions`
Object containing details of all placed subscriptions:
```json
{
    "management": "subscription-id-or-empty",
    "connectivity": "subscription-id-or-empty", 
    "identity": "subscription-id-or-empty",
    "platform": "subscription-id-or-empty",
    "onlineLandingZones": ["array-of-subscription-ids"],
    "corpLandingZones": ["array-of-subscription-ids"],
    "corpConnectedLandingZones": ["array-of-subscription-objects"]
}
```

### `summary`
Deployment summary with statistics:
```json
{
    "totalSubscriptionsPlaced": 5,
    "deploymentMode": "Full",
    "platformSubscriptionsCount": 3,
    "landingZoneSubscriptionsCount": 2
}
```

## ⚙️ Advanced Configuration

### Custom Management Group Names
```json
{
    "managementGroups": {
        "root": "mycompany",
        "platform": "mycompany-platform",
        "management": "mycompany-mgmt",
        "connectivity": "mycompany-conn",
        "identity": "mycompany-id",
        "landingZones": "mycompany-lz",
        "corp": "mycompany-corp",
        "online": "mycompany-online"
    }
}
```

### Corp Connected Landing Zones Format
```json
{
    "corpConnectedLzSubscriptionId": [
        {
            "subs": "subscription-id-1",
            "vnetName": "vnet-name-1",
            "resourceGroupName": "rg-name-1"
        },
        {
            "subs": "subscription-id-2", 
            "vnetName": "vnet-name-2",
            "resourceGroupName": "rg-name-2"
        }
    ]
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure you have Management Group Contributor role
   - Verify subscription access permissions

2. **Subscription Already Placed**
   - Check if subscription is already in a management group
   - Use Azure Portal to verify current placement

3. **Management Group Not Found**
   - Ensure management groups are created before running this module
   - Verify management group names match exactly

### Validation Commands

```powershell
# Check current subscription placement
Get-AzManagementGroupSubscription -GroupName "your-mg-name"

# Verify management group structure
Get-AzManagementGroup -GroupName "your-root-mg" -Expand -Recurse
```

## 🔗 Dependencies

- **Management Groups**: Must be created before subscription placement
- **Azure PowerShell**: Az.Accounts and Az.Resources modules
- **Permissions**: Management Group Contributor role

## 📚 Related Modules

- [Management Groups Module](../management-groups/README.md)
- [Policy Assignments Module](../policy-assignments/README.md)
- [Role Assignments Module](../role-assignments/README.md)

## 🤝 Contributing

When modifying this module:

1. Update parameter descriptions
2. Test both Full and Lite modes
3. Validate with different subscription combinations
4. Update documentation and examples
5. Test integration scenarios

## 📄 License

This module is part of the Azure Landing Zone accelerator and follows the same licensing terms.
