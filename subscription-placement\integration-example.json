{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Subscription Placement Integration Example", "version": "1.0.0"}, "description": "Example showing how to integrate subscription placement module into main ALZ template"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "managementSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated management subscription."}}, "connectivitySubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated connectivity subscription."}}, "identitySubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated identity subscription."}}, "singlePlatformSubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the single platform subscription (lite deployment)."}}, "corpLzSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for existing, empty subscriptions you want to move in as your first corp landing zones."}}, "onlineLzSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for existing, empty subscriptions you want to move in as your first online landing zones."}}, "corpConnectedLzSubscriptionId": {"type": "array", "defaultValue": [], "metadata": {"description": "Provide the subscription ids for existing, empty subscriptions you want to move in as your first corp landing zones and connect to virtual networking hub."}}}, "variables": {"deploymentSuffix": "[concat('-', deployment().location, '-', guid(deployment().name))]", "enableLite": "[not(empty(parameters('singlePlatformSubscriptionId')))]", "managementGroups": {"root": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-platform')]", "management": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-management')]", "connectivity": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-connectivity')]", "identity": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-identity')]", "landingZones": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-landingzones')]", "corp": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-corp')]", "online": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-online')]", "decommissioned": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-decommissioned')]", "sandbox": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-sandbox')]"}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2020-10-01", "name": "[concat('alz-management-groups', variables('deploymentSuffix'))]", "location": "[deployment().location]", "properties": {"mode": "Incremental", "templateLink": {"uri": "[uri(deployment().properties.templateLink.uri, 'management-groups/deploy-management-groups.json')]", "contentVersion": "*******"}, "parameters": {"enterpriseScaleCompanyPrefix": {"value": "[parameters('enterpriseScaleCompanyPrefix')]"}, "enableLite": {"value": "[variables('enableLite')]"}}}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2020-10-01", "name": "[concat('alz-subscription-placement', variables('deploymentSuffix'))]", "location": "[deployment().location]", "dependsOn": ["[resourceId('Microsoft.Resources/deployments', concat('alz-management-groups', variables('deploymentSuffix')))]"], "properties": {"mode": "Incremental", "templateLink": {"uri": "[uri(deployment().properties.templateLink.uri, 'subscription-placement/deploy-subscription-placement.json')]", "contentVersion": "*******"}, "parameters": {"enterpriseScaleCompanyPrefix": {"value": "[parameters('enterpriseScaleCompanyPrefix')]"}, "managementSubscriptionId": {"value": "[parameters('managementSubscriptionId')]"}, "connectivitySubscriptionId": {"value": "[parameters('connectivitySubscriptionId')]"}, "identitySubscriptionId": {"value": "[parameters('identitySubscriptionId')]"}, "singlePlatformSubscriptionId": {"value": "[parameters('singlePlatformSubscriptionId')]"}, "corpLzSubscriptionId": {"value": "[parameters('corpLzSubscriptionId')]"}, "onlineLzSubscriptionId": {"value": "[parameters('onlineLzSubscriptionId')]"}, "corpConnectedLzSubscriptionId": {"value": "[parameters('corpConnectedLzSubscriptionId')]"}}}}], "outputs": {"managementGroupsResult": {"type": "object", "value": "[reference(concat('alz-management-groups', variables('deploymentSuffix')))]"}, "subscriptionPlacementResult": {"type": "object", "value": "[reference(concat('alz-subscription-placement', variables('deploymentSuffix')))]"}, "placedSubscriptions": {"type": "object", "value": "[reference(concat('alz-subscription-placement', variables('deploymentSuffix'))).outputs.placedSubscriptions.value]"}, "deploymentSummary": {"type": "object", "value": {"deploymentMode": "[if(variables('enableLite'), 'Lite', 'Full')]", "managementGroupsCreated": "[reference(concat('alz-management-groups', variables('deploymentSuffix'))).outputs.summary.value.totalManagementGroupsCreated]", "subscriptionsPlaced": "[reference(concat('alz-subscription-placement', variables('deploymentSuffix'))).outputs.summary.value.totalSubscriptionsPlaced]", "platformSubscriptions": "[reference(concat('alz-subscription-placement', variables('deploymentSuffix'))).outputs.summary.value.platformSubscriptionsCount]", "landingZoneSubscriptions": "[reference(concat('alz-subscription-placement', variables('deploymentSuffix'))).outputs.summary.value.landingZoneSubscriptionsCount]"}}}}