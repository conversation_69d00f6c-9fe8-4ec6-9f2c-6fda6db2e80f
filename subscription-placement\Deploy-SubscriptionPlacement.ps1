#Requires -Version 7

<#
.SYNOPSIS
    Deploy Azure Landing Zone Subscription Placement Module

.DESCRIPTION
    This script deploys the subscription placement module which places subscriptions into appropriate management groups.
    Supports both Full and Lite deployment modes.

.PARAMETER EnterpriseScaleCompanyPrefix
    Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy.

.PARAMETER Location
    Azure region for the deployment.

.PARAMETER ManagementSubscriptionId
    Subscription ID for the dedicated management subscription.

.PARAMETER ConnectivitySubscriptionId
    Subscription ID for the dedicated connectivity subscription.

.PARAMETER IdentitySubscriptionId
    Subscription ID for the dedicated identity subscription.

.PARAMETER SinglePlatformSubscriptionId
    Subscription ID for the single platform subscription (lite deployment).

.PARAMETER LdzPrdLegacySubscriptionId
    Array of subscription IDs for production legacy applications.

.PARAMETER LdzPrdMicrosvcSubscriptionId
    Array of subscription IDs for production microservices applications.

.PARAMETER LdzNonPrdUatSubscriptionId
    Array of subscription IDs for UAT environment.

.PARAMETER LdzNonPrdDevSubscriptionId
    Array of subscription IDs for development environment.

.PARAMETER SandboxSubscriptionId
    Array of subscription IDs for sandbox environment.

.PARAMETER TemplateUri
    URI to the template. If not provided, uses local template.

.PARAMETER WhatIf
    Performs a what-if deployment to show what resources would be created.

.EXAMPLE
    .\Deploy-SubscriptionPlacement.ps1 -EnterpriseScaleCompanyPrefix "contoso" -Location "East US" -ManagementSubscriptionId "12345678-1234-1234-1234-123456789012"

.EXAMPLE
    .\Deploy-SubscriptionPlacement.ps1 -EnterpriseScaleCompanyPrefix "contoso" -Location "East US" -SinglePlatformSubscriptionId "12345678-1234-1234-1234-123456789012"

.NOTES
    Author: Azure Landing Zone Team
    Version: 1.0.0
#>

[CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $true)]
    [string]$Location,

    [Parameter(Mandatory = $false)]
    [string]$ManagementSubscriptionId = "",

    [Parameter(Mandatory = $false)]
    [string]$ConnectivitySubscriptionId = "",

    [Parameter(Mandatory = $false)]
    [string]$IdentitySubscriptionId = "",

    [Parameter(Mandatory = $false)]
    [string]$SinglePlatformSubscriptionId = "",

    [Parameter(Mandatory = $false)]
    [string[]]$LdzPrdLegacySubscriptionId = @(),

    [Parameter(Mandatory = $false)]
    [string[]]$LdzPrdMicrosvcSubscriptionId = @(),

    [Parameter(Mandatory = $false)]
    [string[]]$LdzNonPrdUatSubscriptionId = @(),

    [Parameter(Mandatory = $false)]
    [string[]]$LdzNonPrdDevSubscriptionId = @(),

    [Parameter(Mandatory = $false)]
    [string[]]$SandboxSubscriptionId = @(),

    [Parameter(Mandatory = $false)]
    [string]$TemplateUri,

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Import required modules
try {
    Import-Module Az.Accounts -Force
    Import-Module Az.Resources -Force
}
catch {
    Write-Error "Failed to import required Azure PowerShell modules. Please ensure Az.Accounts and Az.Resources are installed."
    exit 1
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor Green
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor Green

# Determine deployment mode
$isLiteMode = -not [string]::IsNullOrEmpty($SinglePlatformSubscriptionId)
$deploymentMode = if ($isLiteMode) { "Lite" } else { "Full" }

Write-Host "`nDeployment Configuration:" -ForegroundColor Cyan
Write-Host "  Company Prefix: $EnterpriseScaleCompanyPrefix" -ForegroundColor Cyan
Write-Host "  Location: $Location" -ForegroundColor Cyan
Write-Host "  Deployment Mode: $deploymentMode" -ForegroundColor Cyan

if ($isLiteMode) {
    Write-Host "  Platform Subscription: $SinglePlatformSubscriptionId" -ForegroundColor Cyan
} else {
    if ($ManagementSubscriptionId) { Write-Host "  Management Subscription: $ManagementSubscriptionId" -ForegroundColor Cyan }
    if ($ConnectivitySubscriptionId) { Write-Host "  Connectivity Subscription: $ConnectivitySubscriptionId" -ForegroundColor Cyan }
    if ($IdentitySubscriptionId) { Write-Host "  Identity Subscription: $IdentitySubscriptionId" -ForegroundColor Cyan }
}

if ($LdzPrdLegacySubscriptionId.Count -gt 0) {
    Write-Host "  Production Legacy Subscriptions: $($LdzPrdLegacySubscriptionId.Count)" -ForegroundColor Cyan
}
if ($LdzPrdMicrosvcSubscriptionId.Count -gt 0) {
    Write-Host "  Production Microservices Subscriptions: $($LdzPrdMicrosvcSubscriptionId.Count)" -ForegroundColor Cyan
}
if ($LdzNonPrdUatSubscriptionId.Count -gt 0) {
    Write-Host "  UAT Environment Subscriptions: $($LdzNonPrdUatSubscriptionId.Count)" -ForegroundColor Cyan
}
if ($LdzNonPrdDevSubscriptionId.Count -gt 0) {
    Write-Host "  Development Environment Subscriptions: $($LdzNonPrdDevSubscriptionId.Count)" -ForegroundColor Cyan
}
if ($SandboxSubscriptionId.Count -gt 0) {
    Write-Host "  Sandbox Environment Subscriptions: $($SandboxSubscriptionId.Count)" -ForegroundColor Cyan
}

# Prepare template and parameters
$templateFile = if ($TemplateUri) { $TemplateUri } else { Join-Path $PSScriptRoot "deploy-subscription-placement.json" }
$deploymentName = "alz-subscription-placement-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

# Prepare parameters
$templateParameters = @{
    enterpriseScaleCompanyPrefix = $EnterpriseScaleCompanyPrefix
    managementSubscriptionId = $ManagementSubscriptionId
    connectivitySubscriptionId = $ConnectivitySubscriptionId
    identitySubscriptionId = $IdentitySubscriptionId
    singlePlatformSubscriptionId = $SinglePlatformSubscriptionId
    ldzPrdLegacySubscriptionId = $LdzPrdLegacySubscriptionId
    ldzPrdMicrosvcSubscriptionId = $LdzPrdMicrosvcSubscriptionId
    ldzNonPrdUatSubscriptionId = $LdzNonPrdUatSubscriptionId
    ldzNonPrdDevSubscriptionId = $LdzNonPrdDevSubscriptionId
    sandboxSubscriptionId = $SandboxSubscriptionId
}

# Deploy template
try {
    Write-Host "`nStarting deployment..." -ForegroundColor Yellow
    
    $deploymentParams = @{
        Name = $deploymentName
        Location = $Location
        TemplateFile = $templateFile
        TemplateParameterObject = $templateParameters
        Verbose = $true
    }

    if ($WhatIf) {
        $deploymentParams.Add("WhatIf", $true)
        Write-Host "Performing What-If deployment..." -ForegroundColor Yellow
    }

    $deployment = New-AzManagementGroupDeployment @deploymentParams

    if (-not $WhatIf) {
        Write-Host "`nDeployment completed successfully!" -ForegroundColor Green
        Write-Host "Deployment Name: $deploymentName" -ForegroundColor Green
        
        if ($deployment.Outputs) {
            Write-Host "`nDeployment Summary:" -ForegroundColor Green
            $summary = $deployment.Outputs.summary.Value
            Write-Host "  Total Subscriptions Placed: $($summary.totalSubscriptionsPlaced)" -ForegroundColor Green
            Write-Host "  Platform Subscriptions: $($summary.platformSubscriptionsCount)" -ForegroundColor Green
            Write-Host "  Landing Zone Subscriptions: $($summary.landingZoneSubscriptionsCount)" -ForegroundColor Green
        }
    }
}
catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
}
