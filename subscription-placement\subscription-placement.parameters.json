{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"managementGroups": {"value": {"root": "contoso", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "Landing Zone", "ldz-prd": "ldz-prd", "ldz-prd-legacy": "ldz-prd-legacy", "ldz-prd-microsvc": "ldz-prd-microsvc", "ldz-non-prd": "ldz-non-prd", "ldz-non-prd-uat": "ldz-non-prd-uat", "ldz-non-prd-dev": "ldz-non-prd-dev", "sandbox": "Sandbox", "decommissioned": "Decommissioned"}}, "managementSubscriptionId": {"value": ""}, "connectivitySubscriptionId": {"value": ""}, "identitySubscriptionId": {"value": ""}, "singlePlatformSubscriptionId": {"value": ""}, "ldzPrdLegacySubscriptionId": {"value": []}, "ldzPrdMicrosvcSubscriptionId": {"value": []}, "ldzNonPrdUatSubscriptionId": {"value": []}, "ldzNonPrdDevSubscriptionId": {"value": []}, "sandboxSubscriptionId": {"value": []}, "enableLite": {"value": false}}}