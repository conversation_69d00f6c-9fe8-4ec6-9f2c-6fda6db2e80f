param(
    [Parameter(Mandatory = $true)]
    [string]$EnterpriseScaleCompanyPrefix,
    
    [Parameter(Mandatory = $false)]
    [switch]$EnableLite = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "=== Azure Landing Zone Management Groups Deployment (PowerShell) ===" -ForegroundColor Green
Write-Host "Company Prefix: $EnterpriseScaleCompanyPrefix" -ForegroundColor Cyan
Write-Host "Deployment Mode: $(if ($EnableLite) { 'Lite' } else { 'Full' })" -ForegroundColor Cyan

# Get current context
$context = Get-AzContext
if (-not $context) {
    Write-Error "No Azure context found. Please run Connect-AzAccount first."
    exit 1
}

$tenantRootGroupId = "/providers/Microsoft.Management/managementGroups/$($context.Tenant.Id)"
Write-Host "Tenant Root Group: $($context.Tenant.Id)" -ForegroundColor Cyan

try {
    # 1. Create root management group
    Write-Host "`nCreating root management group: $EnterpriseScaleCompanyPrefix" -ForegroundColor Yellow
    $rootMg = New-AzManagementGroup -GroupName $EnterpriseScaleCompanyPrefix -DisplayName $EnterpriseScaleCompanyPrefix -ParentId $tenantRootGroupId
    Write-Host "Created: $($rootMg.Name)" -ForegroundColor Green
    
    # 2. Create Platform management group
    Write-Host "`nCreating Platform management group..." -ForegroundColor Yellow
    $platformMg = New-AzManagementGroup -GroupName "Platform" -DisplayName "Platform Services" -ParentId $rootMg.Id
    Write-Host "Created: $($platformMg.Name)" -ForegroundColor Green
    
    # 3. Create Platform sub-groups (only for Full mode)
    if (-not $EnableLite) {
        Write-Host "`nCreating Platform Management sub-group..." -ForegroundColor Yellow
        $mgmtMg = New-AzManagementGroup -GroupName "mg-Platform-Management" -DisplayName "Platform Management Services" -ParentId $platformMg.Id
        Write-Host "Created: $($mgmtMg.Name)" -ForegroundColor Green
        
        Write-Host "`nCreating Platform Connectivity sub-group..." -ForegroundColor Yellow
        $connMg = New-AzManagementGroup -GroupName "mg-Platform-Connectivity" -DisplayName "Platform Connectivity Services" -ParentId $platformMg.Id
        Write-Host "Created: $($connMg.Name)" -ForegroundColor Green
    }
    
    # 4. Create Landing Zone management group
    Write-Host "`nCreating Landing Zone management group..." -ForegroundColor Yellow
    $lzMg = New-AzManagementGroup -GroupName "Landing-Zone" -DisplayName "Application Landing Zones" -ParentId $rootMg.Id
    Write-Host "Created: $($lzMg.Name)" -ForegroundColor Green
    
    # 5. Create Production Landing Zone
    Write-Host "`nCreating Production Landing Zone..." -ForegroundColor Yellow
    $prdMg = New-AzManagementGroup -GroupName "ldz-prd" -DisplayName "Production Landing Zones" -ParentId $lzMg.Id
    Write-Host "Created: $($prdMg.Name)" -ForegroundColor Green
    
    # 6. Create Production sub-groups
    Write-Host "`nCreating Production Legacy sub-group..." -ForegroundColor Yellow
    $prdLegacyMg = New-AzManagementGroup -GroupName "ldz-prd-legacy" -DisplayName "Production Legacy Applications" -ParentId $prdMg.Id
    Write-Host "Created: $($prdLegacyMg.Name)" -ForegroundColor Green
    
    Write-Host "`nCreating Production Microservices sub-group..." -ForegroundColor Yellow
    $prdMicrosvcMg = New-AzManagementGroup -GroupName "ldz-prd-microsvc" -DisplayName "Production Microservices Applications" -ParentId $prdMg.Id
    Write-Host "Created: $($prdMicrosvcMg.Name)" -ForegroundColor Green
    
    # 7. Create Non-Production Landing Zone
    Write-Host "`nCreating Non-Production Landing Zone..." -ForegroundColor Yellow
    $nonPrdMg = New-AzManagementGroup -GroupName "ldz-non-prd" -DisplayName "Non-Production Landing Zones" -ParentId $lzMg.Id
    Write-Host "Created: $($nonPrdMg.Name)" -ForegroundColor Green
    
    # 8. Create Non-Production sub-groups
    Write-Host "`nCreating UAT Environment sub-group..." -ForegroundColor Yellow
    $uatMg = New-AzManagementGroup -GroupName "ldz-non-prd-uat" -DisplayName "UAT Environment" -ParentId $nonPrdMg.Id
    Write-Host "Created: $($uatMg.Name)" -ForegroundColor Green
    
    Write-Host "`nCreating Development Environment sub-group..." -ForegroundColor Yellow
    $devMg = New-AzManagementGroup -GroupName "ldz-non-prd-dev" -DisplayName "Development Environment" -ParentId $nonPrdMg.Id
    Write-Host "Created: $($devMg.Name)" -ForegroundColor Green
    
    # 9. Create Sandbox management group
    Write-Host "`nCreating Sandbox management group..." -ForegroundColor Yellow
    $sandboxMg = New-AzManagementGroup -GroupName "Sandbox" -DisplayName "Sandbox Environment" -ParentId $rootMg.Id
    Write-Host "Created: $($sandboxMg.Name)" -ForegroundColor Green
    
    # 10. Create Decommissioned management group
    Write-Host "`nCreating Decommissioned management group..." -ForegroundColor Yellow
    $decommMg = New-AzManagementGroup -GroupName "Decommissioned" -DisplayName "Decommissioned Subscriptions" -ParentId $rootMg.Id
    Write-Host "Created: $($decommMg.Name)" -ForegroundColor Green
    
    Write-Host "`n=== Deployment Completed Successfully ===" -ForegroundColor Green
    Write-Host "Created management group structure for: $EnterpriseScaleCompanyPrefix" -ForegroundColor Green
    
} catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
}
