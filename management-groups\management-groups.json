{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "metadata": {
        "_generator": {
            "name": "ALZ Management Groups Module",
            "version": "1.0.0"
        },
        "description": "This template creates the Azure Landing Zone management group structure"
    },
    "parameters": {
        "enterpriseScaleCompanyPrefix": {
            "type": "string",
            "metadata": {
                "description": "Provide the company prefix for the management group structure"
            }
        },
        "enableLite": {
            "type": "bool",
            "defaultValue": false,
            "metadata": {
                "description": "Enable Enterprise Scale Lite deployment (single platform subscription)"
            }
        }
    },
    "variables": {
        // Declaring the prescriptive management group structure that will be used in the scope construction
        "mgmtGroups": {
            "eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]",
            "platform": "Platform",
            "management": "mg-Platform-Management",
            "connectivity": "mg-Platform-Connectivity",
            "lzs": "Landing Zone",
            "ldz-prd": "ldz-prd",
            "ldz-prd-legacy": "ldz-prd-legacy",
            "ldz-prd-microsvc": "ldz-prd-microsvc",
            "ldz-non-prd": "ldz-non-prd",
            "ldz-non-prd-uat": "ldz-non-prd-uat",
            "ldz-non-prd-dev": "ldz-non-prd-dev",
            "sandbox": "Sandbox",
            "decommissioned": "Decommissioned"
        },
        "mgmtGroupsLite": {
            "eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]",
            "platform": "Platform",
            "lzs": "Landing Zone",
            "ldz-prd": "ldz-prd",
            "ldz-prd-legacy": "ldz-prd-legacy",
            "ldz-prd-microsvc": "ldz-prd-microsvc",
            "ldz-non-prd": "ldz-non-prd",
            "ldz-non-prd-uat": "ldz-non-prd-uat",
            "ldz-non-prd-dev": "ldz-non-prd-dev",
            "sandbox": "Sandbox",
            "decommissioned": "Decommissioned"
        },
        "selectedMgmtGroups": "[if(parameters('enableLite'), variables('mgmtGroupsLite'), variables('mgmtGroups'))]",
        "copy": [
            {
                "name": "mgmtGroupsArray",
                "count": "[length(items(variables('selectedMgmtGroups')))]",
                "input": "[items(variables('selectedMgmtGroups'))[copyIndex('mgmtGroupsArray')].value]"
            }
        ],
        // Declaring scopes that will be used for optional deployments
        "scopes": {
            "eslzRootManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]",
            "platformManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]",
            "managementManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').management))]",
            "connectivityManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').connectivity))]",
            "lzsManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]",
            "ldzPrdManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-prd'])]",
            "ldzPrdLegacyManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-prd-legacy'])]",
            "ldzPrdMicrosvcManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-prd-microsvc'])]",
            "ldzNonPrdManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-non-prd'])]",
            "ldzNonPrdUatManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-non-prd-uat'])]",
            "ldzNonPrdDevManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-non-prd-dev'])]",
            "sandboxManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').sandbox)]",
            "decommissionedManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').decommissioned)]"
        }
    },
    "resources": [
        {
            // Creating the root management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').eslzRoot]",
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').eslzRoot]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', tenant().tenantId)]"
                    }
                }
            }
        },
        {
            // Creating the platform management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').platform]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "Platform Services",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        },
        {
            // Creating the landing zones management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').lzs]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "Application Landing Zones",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        },
        {
            // Creating the management management group (only for full deployment)
            "condition": "[not(parameters('enableLite'))]",
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').management]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
            ],
            "properties": {
                "displayName": "Platform Management Services",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
                    }
                }
            }
        },
        {
            // Creating the connectivity management group (only for full deployment)
            "condition": "[not(parameters('enableLite'))]",
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').connectivity]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
            ],
            "properties": {
                "displayName": "Platform Connectivity Services",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
                    }
                }
            }
        },
        {
            // Creating the ldz-prd management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups')['ldz-prd']]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
            ],
            "properties": {
                "displayName": "Production Landing Zones",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
                    }
                }
            }
        },
        {
            // Creating the ldz-prd-legacy management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups')['ldz-prd-legacy']]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-prd'])]"
            ],
            "properties": {
                "displayName": "Production Legacy Applications",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-prd'])]"
                    }
                }
            }
        },
        {
            // Creating the ldz-prd-microsvc management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups')['ldz-prd-microsvc']]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-prd'])]"
            ],
            "properties": {
                "displayName": "Production Microservices Applications",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-prd'])]"
                    }
                }
            }
        },
        {
            // Creating the ldz-non-prd management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups')['ldz-non-prd']]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
            ],
            "properties": {
                "displayName": "Non-Production Landing Zones",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
                    }
                }
            }
        },
        {
            // Creating the ldz-non-prd-uat management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups')['ldz-non-prd-uat']]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-non-prd'])]"
            ],
            "properties": {
                "displayName": "UAT Environment",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-non-prd'])]"
                    }
                }
            }
        },
        {
            // Creating the ldz-non-prd-dev management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups')['ldz-non-prd-dev']]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-non-prd'])]"
            ],
            "properties": {
                "displayName": "Development Environment",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups')['ldz-non-prd'])]"
                    }
                }
            }
        },
        {
            // Creating the decommissioned management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').decommissioned]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "Decommissioned Subscriptions",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        },
        {
            // Creating the sandbox management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').sandbox]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "Sandbox Environment",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        }
    ],
    "outputs": {
        "managementGroups": {
            "type": "object",
            "value": "[variables('selectedMgmtGroups')]"
        },
        "scopes": {
            "type": "object",
            "value": "[variables('scopes')]"
        },
        "mgmtGroupsArray": {
            "type": "array",
            "value": "[variables('mgmtGroupsArray')]"
        },
        "summary": {
            "type": "object",
            "value": {
                "totalManagementGroupsCreated": "[if(parameters('enableLite'), 9, 11)]",
                "deploymentMode": "[if(parameters('enableLite'), 'Lite', 'Full')]",
                "platformManagementGroups": "[if(parameters('enableLite'), 1, 3)]",
                "landingZoneManagementGroups": 6,
                "rootLevelManagementGroups": 3
            }
        }
    }
}
