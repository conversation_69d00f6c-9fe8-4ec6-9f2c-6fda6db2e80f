{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "metadata": {
        "_generator": {
            "name": "ALZ Management Groups Module",
            "version": "1.0.0"
        },
        "description": "This template creates the Azure Landing Zone management group structure"
    },
    "parameters": {
        "enterpriseScaleCompanyPrefix": {
            "type": "string",
            "metadata": {
                "description": "Provide the company prefix for the management group structure"
            }
        },
        "enableLite": {
            "type": "bool",
            "defaultValue": false,
            "metadata": {
                "description": "Enable Enterprise Scale Lite deployment (single platform subscription)"
            }
        }
    },
    "variables": {
        // Declaring the prescriptive management group structure that will be used in the scope construction
        "mgmtGroups": {
            "eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]",
            "platform": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'platform')]",
            "management": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'management')]",
            "connectivity": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'connectivity')]",
            "identity": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'identity')]",
            "lzs": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'landingzones')]",
            "corp": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'corp')]",
            "online": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'online')]",
            "decommissioned": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'decommissioned')]",
            "sandboxes": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'sandboxes')]"
        },
        "mgmtGroupsLite": {
            "eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]",
            "platform": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'platform')]",
            "lzs": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'landingzones')]",
            "corp": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'corp')]",
            "online": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'online')]",
            "decommissioned": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'decommissioned')]",
            "sandboxes": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', 'sandboxes')]"
        },
        "selectedMgmtGroups": "[if(parameters('enableLite'), variables('mgmtGroupsLite'), variables('mgmtGroups'))]",
        "copy": [
            {
                "name": "mgmtGroupsArray",
                "count": "[length(items(variables('selectedMgmtGroups')))]",
                "input": "[items(variables('selectedMgmtGroups'))[copyIndex('mgmtGroupsArray')].value]"
            }
        ],
        // Declaring scopes that will be used for optional deployments
        "scopes": {
            "eslzRootManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]",
            "platformManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]",
            "managementManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').management))]",
            "connectivityManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').connectivity))]",
            "identityManagementGroup": "[if(parameters('enableLite'), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform), tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').identity))]",
            "lzsManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]",
            "corpManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').corp)]",
            "onlineManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').online)]",
            "decommissionedManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').decommissioned)]",
            "sandboxManagementGroup": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').sandboxes)]"
        }
    },
    "resources": [
        {
            // Creating the root management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').eslzRoot]",
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').eslzRoot]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', tenant().tenantId)]"
                    }
                }
            }
        },
        {
            // Creating the platform management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').platform]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').platform]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        },
        {
            // Creating the landing zones management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').lzs]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').lzs]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        },
        {
            // Creating the management management group (only for full deployment)
            "condition": "[not(parameters('enableLite'))]",
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').management]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').management]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
                    }
                }
            }
        },
        {
            // Creating the connectivity management group (only for full deployment)
            "condition": "[not(parameters('enableLite'))]",
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').connectivity]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').connectivity]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
                    }
                }
            }
        },
        {
            // Creating the identity management group (only for full deployment)
            "condition": "[not(parameters('enableLite'))]",
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').identity]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').identity]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').platform)]"
                    }
                }
            }
        },
        {
            // Creating the corp landing zone management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').corp]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').corp]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
                    }
                }
            }
        },
        {
            // Creating the online landing zone management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').online]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').online]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').lzs)]"
                    }
                }
            }
        },
        {
            // Creating the decommissioned management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').decommissioned]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').decommissioned]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        },
        {
            // Creating the sandbox management group
            "type": "Microsoft.Management/managementGroups",
            "apiVersion": "2020-05-01",
            "name": "[variables('selectedMgmtGroups').sandboxes]",
            "dependsOn": [
                "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
            ],
            "properties": {
                "displayName": "[variables('selectedMgmtGroups').sandboxes]",
                "details": {
                    "parent": {
                        "id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('selectedMgmtGroups').eslzRoot)]"
                    }
                }
            }
        }
    ],
    "outputs": {
        "managementGroups": {
            "type": "object",
            "value": "[variables('selectedMgmtGroups')]"
        },
        "scopes": {
            "type": "object",
            "value": "[variables('scopes')]"
        },
        "mgmtGroupsArray": {
            "type": "array",
            "value": "[variables('mgmtGroupsArray')]"
        }
    }
}
